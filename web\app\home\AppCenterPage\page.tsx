'use client'
import AppCenterTitle from '@/app/home/<USER>/components/Title'
import AppCenterSearch from '@/app/home/<USER>/components/search'
import AppCenterItem from '@/app/home/<USER>/components/appItem'
import { COMMON } from '@/app/home/<USER>'
import { useEffect, useState } from 'react'
import useSWRInfinite from 'swr/infinite'
import { fetchAppList } from '@/service/apps'
import type { AppListResponse } from '@/models/app'

const getKey = (
  pageIndex: number,
  previousPageData: AppListResponse,
  activeTab: string,
  isCreatedByMe: boolean,
  tags: string[],
  keywords: string,
) => {
  if (!pageIndex || previousPageData.has_more) {
    const params: any = { url: 'apps', params: { page: pageIndex + 1, limit: 30, name: keywords, is_created_by_me: isCreatedByMe } }

    if (activeTab !== 'all')
      params.params.mode = activeTab
    else
      delete params.params.mode

    if (tags.length)
      params.params.tag_ids = tags

    return params
  }
  return null
}
export default function AppCenterPage() {
  const [selectBtn, setSelectBtn] = useState(0)
  const [activeTab, setActiveTab] = useState('all')
  const [isCreatedByMe, setIsCreatedByMe] = useState(false)
  const [tagIDs, setTagIDs] = useState<string[]>([])
  const [searchKeywords, setSearchKeywords] = useState('')
  const { data, isLoading, error, setSize, mutate } = useSWRInfinite(
    (pageIndex: number, previousPageData: AppListResponse) => getKey(pageIndex, previousPageData, activeTab, isCreatedByMe, tagIDs, searchKeywords),
    fetchAppList,
    {
      revalidateFirstPage: true,
      shouldRetryOnError: false,
      dedupingInterval: 500,
      errorRetryCount: 3,
    },
  )
  useEffect(() => {
    setActiveTab(COMMON.appCenterSelectItems[selectBtn].mode)
  }, [selectBtn])

  useEffect(() => {
    if (error?.isHomePageUnauth && window.showHomeLoginDialog)
      window.showHomeLoginDialog()
  }, [error])
  return (
    <>
      <div className="size-full px-[300px] py-[75px]">
        {/* 标题 */}
        <div className="flex items-start pb-[50px]">
          <AppCenterTitle />
        </div>
        {/* 分类按钮及搜索 */}
          <AppCenterSearch selectItems={COMMON.appCenterSelectItems} selectBtn={selectBtn} setSelectBtn={setSelectBtn} searchKeywords={searchKeywords} setSearchKeywords={setSearchKeywords} />
        {/* 应用选择 */}
        <div className="grid grid-cols-3 gap-4 pt-[21px]">
          {
            data?.map((item: any) => {
              return item.data.map((app: any) => {
                return <AppCenterItem app={app} key={app.id} />
              })
            })
          }
        </div>
      </div>
    </>
  )
}
