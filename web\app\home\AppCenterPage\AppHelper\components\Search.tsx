'use client'
import { useState } from 'react'
import searchIcon from '@/assets/images/home/<USER>'
import searchNowIcon from '@/assets/images/home/<USER>'
import { SimpleSelect } from '@/app/components/base/select'
import { COMMON } from '@/app/home/<USER>'
import { useRouter } from 'next/navigation'
export default function Search() {
  const [input, setInput] = useState('')
  const router = useRouter()
  return (
    <>
      <div className="relative mb-8 w-[900px] rounded-[20px] border border-[#ECF2FF] bg-[#FFFFFF] px-[20px] py-[16px]">
        <textarea
          className="relative mb-[40px] h-[160px] w-full resize-none bg-transparent text-[16px] font-[400] leading-[22px] outline-none"
          placeholder={COMMON.inputPlaceholder}
          value={input}
          onChange={e => setInput(e.target.value)}
          onKeyDown={(e) => {
            if (input.trim().length > 0 && e.key === 'Enter' && !e.shiftKey) {
              e.preventDefault()
              router.push(`/home/<USER>/${COMMON.appId}?question=${encodeURIComponent(input)}`)
            }
          }}
        />
        {/* SimpleSelect 左下角 */}
        <div className="absolute bottom-4 left-4 z-10">
          <SimpleSelect
            items={COMMON.selectItems}
            defaultValue={COMMON.selectItems[0].value}
            onSelect={item => console.log(item)}
            placeholder="请选择模型"
            notClearable={true}
            isLoading={false}
            className="h-[32px] min-w-[120px] rounded-[16px] border-[#DFDFDF] bg-[#F9FAFC]"
          />
        </div>
        {/* 搜索按钮 右下角 */}
        <button className="absolute bottom-4 right-4 z-10 flex size-[34px] items-center justify-center"
          disabled={input.trim().length === 0}
        >
          <span role="img" aria-label="search">
            {
              input.trim().length > 0 ? (
                <img src={searchNowIcon.src} alt="" className="size-[34px]" onClick={() => {
                  router.push(`/home/<USER>/${COMMON.appId}?question=${encodeURIComponent(input)}`)
                }}/>
              ) : (
                <img src={searchIcon.src} alt="" className="size-[34px]" />
              )
            }
          </span>
        </button>
      </div>
    </>
  )
}
