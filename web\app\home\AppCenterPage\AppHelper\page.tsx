'use client'
import AppHelperTitle from '@/app/home/<USER>/AppHelper/components/Title'
import AppHelperSearch from '@/app/home/<USER>/AppHelper/components/Search'
import AppHelperQuestion from '@/app/home/<USER>/AppHelper/components/Question'
import { useAppContext } from '@/app/home/<USER>/context/AppContext'
export default function AppHelper() {
  const { selectedApp } = useAppContext()

  return (
    <>
      <div className="flex size-full flex-col items-center py-[124px]" style={{ background: 'linear-gradient( 180deg, #F3F8FF 0%, #F3F8FF 74%, #FFFFFF 100%)' }}>
        {/* 显示选中的应用信息 */}
        {selectedApp && (
          <div className="mb-8 rounded-lg bg-white p-4 shadow-md">
            <div className="flex items-center gap-4">
              <div className="flex size-[60px] items-center justify-center rounded-[50%] bg-[#E9EAF2] text-[40px]">
                {selectedApp.icon}
              </div>
              <div>
                <h3 className="text-lg font-semibold text-[#191919]">{selectedApp.name}</h3>
                <p className="text-sm text-[#9192A9]">{selectedApp.description}</p>
              </div>
            </div>
          </div>
        )}

        {/* 标题 */}
        <AppHelperTitle />
        {/* 输入框 */}
        <AppHelperSearch />
        {/* 推荐问题 */}
        <AppHelperQuestion />
      </div>
    </>
  )
}
