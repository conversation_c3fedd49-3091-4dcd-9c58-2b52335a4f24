'use client'
import type { ReactNode } from 'react'
import React, { createContext, useContext, useState } from 'react'

// 定义应用数据的类型
export interface AppData {
  id?: string
  name: string
  description: string
  icon: React.ReactNode
  // 可以根据需要添加更多字段
}

// 定义Context的类型
interface AppContextType {
  selectedApp: AppData | null
  setSelectedApp: (app: AppData | null) => void
}

// 创建Context
const AppContext = createContext<AppContextType | undefined>(undefined)

// Provider组件
export function AppProvider({ children }: { children: ReactNode }) {
  const [selectedApp, setSelectedApp] = useState<AppData | null>(null)

  return (
    <AppContext.Provider value={{ selectedApp, setSelectedApp }}>
      {children}
    </AppContext.Provider>
  )
}

// 自定义Hook来使用Context
export function useAppContext() {
  const context = useContext(AppContext)
  if (context === undefined)
    throw new Error('useAppContext must be used within an AppProvider')
  return context
}
